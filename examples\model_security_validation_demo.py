#!/usr/bin/env python3
"""
模型安全验证演示
展示模型加载时的严格验证功能
"""

import numpy as np
import os
import sys
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.calibration.neural_network import CalibrationDNN, NetworkConfig
from src.calibration.model_manager import ModelManager, ModelManagerConfig, ModelValidator


def create_test_model():
    """创建一个测试模型"""
    print("创建测试模型...")
    
    config = NetworkConfig(
        input_dim=10,
        output_dim=3,
        layers=[32, 16],
        architecture='feedforward'
    )
    
    dnn = CalibrationDNN(config)
    model = dnn.build_model()
    
    # 训练一点数据以获得有意义的权重
    X_dummy = np.random.randn(100, 10)
    y_dummy = np.random.randn(100, 3)
    model.fit(X_dummy, y_dummy, epochs=2, verbose=0)
    
    return model


def demo_model_validation():
    """演示模型验证功能"""
    print("=" * 60)
    print("模型安全验证演示")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建模型管理器
        config = ModelManagerConfig(
            base_dir=os.path.join(temp_dir, "models"),
            backup_dir=os.path.join(temp_dir, "backups"),
            temp_dir=os.path.join(temp_dir, "temp")
        )
        manager = ModelManager(config)
        
        # 创建和保存测试模型
        model = create_test_model()
        
        print("\n1. 保存测试模型...")
        model_info = manager.save_model(
            model=model,
            model_name="security_test_model",
            version="1.0",
            metadata={
                'description': '安全验证测试模型',
                'author': 'Security Demo',
                'purpose': 'testing'
            }
        )
        print(f"   ✅ 模型已保存: {model_info['model_name']} v{model_info['version']}")
        
        # 2. 验证模型（在加载前）
        print("\n2. 执行加载前验证...")
        validation_result = manager.validate_model_before_load("security_test_model", "1.0")
        
        print(f"   验证状态: {'✅ 通过' if validation_result['is_valid'] else '❌ 失败'}")
        
        if validation_result['errors']:
            print("   错误:")
            for error in validation_result['errors']:
                print(f"     - {error}")
        
        if validation_result['warnings']:
            print("   警告:")
            for warning in validation_result['warnings']:
                print(f"     - {warning}")
        
        if validation_result['security_issues']:
            print("   安全问题:")
            for issue in validation_result['security_issues']:
                print(f"     - {issue}")
        
        # 3. 获取安全报告
        print("\n3. 生成安全报告...")
        security_report = manager.get_model_security_report("security_test_model", "1.0")
        
        print(f"   模型: {security_report['model_name']} v{security_report['version']}")
        print(f"   安全状态: {security_report['security_status']}")
        
        if security_report['recommendations']:
            print("   建议:")
            for rec in security_report['recommendations']:
                print(f"     - {rec}")
        
        # 4. 安全加载模型
        print("\n4. 使用安全加载...")
        try:
            loaded_model, components = manager.load_model_safely(
                "security_test_model", "1.0"
            )
            print("   ✅ 安全加载成功")
            print(f"   模型输入形状: {loaded_model.input_shape}")
            print(f"   模型输出形状: {loaded_model.output_shape}")
            
            # 测试预测
            test_input = np.random.randn(1, 10)
            prediction = loaded_model.predict(test_input, verbose=0)
            print(f"   测试预测形状: {prediction.shape}")
            
        except Exception as e:
            print(f"   ❌ 安全加载失败: {e}")
        
        # 5. 对比普通加载和严格加载
        print("\n5. 对比加载模式...")
        
        # 普通加载（允许警告）
        try:
            model_normal, _ = manager.load_model(
                "security_test_model", "1.0",
                strict_validation=False
            )
            print("   ✅ 普通加载成功（无验证）")
        except Exception as e:
            print(f"   ❌ 普通加载失败: {e}")
        
        # 严格加载（不允许警告）
        try:
            model_strict, _ = manager.load_model(
                "security_test_model", "1.0",
                strict_validation=True,
                allow_warnings=False
            )
            print("   ✅ 严格加载成功（无警告）")
        except Exception as e:
            print(f"   ⚠️ 严格加载失败（有警告）: {e}")
        
        # 严格加载（允许警告）
        try:
            model_strict_warn, _ = manager.load_model(
                "security_test_model", "1.0",
                strict_validation=True,
                allow_warnings=True
            )
            print("   ✅ 严格加载成功（允许警告）")
        except Exception as e:
            print(f"   ❌ 严格加载失败: {e}")
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)


def demo_validator_directly():
    """直接演示验证器功能"""
    print("\n" + "=" * 60)
    print("直接验证器演示")
    print("=" * 60)
    
    # 创建临时模型文件
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建和保存一个简单模型
        model = create_test_model()
        model_path = os.path.join(temp_dir, "test_model.h5")
        model.save(model_path)
        
        print(f"\n测试模型已保存到: {model_path}")
        
        # 创建验证器
        validator = ModelValidator()
        
        # 执行验证
        print("\n执行详细验证...")
        validation_result = validator.validate_model_file(model_path)
        
        print(f"验证结果: {'✅ 通过' if validation_result['is_valid'] else '❌ 失败'}")
        
        # 显示详细信息
        if 'metadata' in validation_result:
            metadata = validation_result['metadata']
            if 'file_size_mb' in metadata:
                print(f"文件大小: {metadata['file_size_mb']:.2f} MB")
        
        if validation_result['warnings']:
            print("警告:")
            for warning in validation_result['warnings']:
                print(f"  - {warning}")
        
        if validation_result['errors']:
            print("错误:")
            for error in validation_result['errors']:
                print(f"  - {error}")
        
        if validation_result['security_issues']:
            print("安全问题:")
            for issue in validation_result['security_issues']:
                print(f"  - {issue}")
        
    finally:
        # 清理
        shutil.rmtree(temp_dir, ignore_errors=True)


def demo_security_scenarios():
    """演示不同安全场景"""
    print("\n" + "=" * 60)
    print("安全场景演示")
    print("=" * 60)
    
    scenarios = [
        {
            'name': '正常模型',
            'description': '标准的训练好的模型',
            'safe': True
        },
        {
            'name': '大型模型',
            'description': '文件大小超过警告阈值的模型',
            'safe': True
        },
        {
            'name': '未编译模型',
            'description': '保存时未编译的模型',
            'safe': True
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. 场景: {scenario['name']}")
        print(f"   描述: {scenario['description']}")
        print(f"   预期安全性: {'✅ 安全' if scenario['safe'] else '❌ 不安全'}")
        
        # 这里可以添加具体的场景测试代码
        # 由于演示目的，我们只显示概念
        print("   状态: 演示场景（实际测试需要具体实现）")


def main():
    """主演示函数"""
    print("模型安全验证功能演示")
    print("本演示将展示以下安全功能:")
    print("1. 模型文件完整性验证")
    print("2. 安全风险检测")
    print("3. 元数据一致性验证")
    print("4. 版本兼容性检查")
    print("5. 严格加载模式")
    
    try:
        demo_model_validation()
        demo_validator_directly()
        demo_security_scenarios()
        
        print("\n" + "=" * 60)
        print("🎉 所有安全验证演示完成！")
        print("=" * 60)
        print("\n安全验证功能可以有效防止:")
        print("- 损坏的模型文件")
        print("- 恶意模型内容")
        print("- 版本不兼容问题")
        print("- 元数据不一致")
        print("- 权重异常值（NaN/Inf）")
        print("\n建议在生产环境中始终使用 load_model_safely() 方法！")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

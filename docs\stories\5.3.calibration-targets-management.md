# Story 5.3: 校准目标管理

## Status

Aproved

## Story

**As a** 研究人员，
**I want** 设置和管理校准目标基准值，
**so that** 确保模型输出与真实流行病学数据一致。

## Acceptance Criteria

1. 实现校准目标数据的导入和管理系统
2. 创建年龄性别特异性基准值配置
3. 实现校准目标的权重和优先级设置
4. 添加基准值数据的质量检查和验证
5. 创建校准目标与模型输出的比较功能
6. 实现校准目标的可视化展示

## Tasks / Subtasks

- [ ] 任务1：实现校准目标数据管理系统 (AC: 1)

  - [ ] 创建src/calibration/targets.py文件
  - [ ] 实现CalibrationTargets类，管理校准目标
  - [ ] 添加多种数据源导入支持（CSV、Excel、JSON、YAML）
  - [ ] 实现目标数据的存储和索引系统
  - [ ] 创建目标数据的版本控制和历史管理
  - [ ] 添加目标数据的备份和恢复功能
- [ ] 任务2：创建年龄性别特异性配置 (AC: 2)

  - [ ] 实现年龄组和性别分层的目标值配置
  - [ ] 添加年龄连续插值和外推功能
  - [ ] 创建性别特异性差异建模
  - [ ] 实现年龄性别交互效应处理
  - [ ] 添加人群特异性目标值调整
  - [ ] 创建目标值的时间趋势建模
- [ ] 任务3：实现权重和优先级设置系统 (AC: 3)

  - [ ] 创建src/calibration/target_weights.py文件
  - [ ] 实现TargetWeightManager类，管理目标权重
  - [ ] 添加基于数据质量的自动权重分配
  - [ ] 实现用户自定义权重配置功能
  - [ ] 创建权重优化和调整算法
  - [ ] 添加权重敏感性分析功能
- [ ] 任务4：添加数据质量检查和验证 (AC: 4)

  - [ ] 创建src/calibration/data_validator.py文件
  - [ ] 实现DataValidator类，验证目标数据质量
  - [ ] 添加数据完整性和一致性检查
  - [ ] 实现异常值检测和处理功能
  - [ ] 创建数据质量评分和报告系统
  - [ ] 添加数据质量改进建议功能
- [ ] 任务5：创建模型输出比较功能 (AC: 5)

  - [ ] 创建src/calibration/target_comparison.py文件
  - [ ] 实现TargetComparator类，比较目标和输出
  - [ ] 添加多种比较指标计算（绝对差异、相对差异等）
  - [ ] 实现拟合优度统计检验功能
  - [ ] 创建比较结果的统计分析
  - [ ] 添加比较结果的自动评估和评分
- [ ] 任务6：实现校准目标可视化展示 (AC: 6)

  - [ ] 创建src/interfaces/desktop/widgets/calibration_targets_widget.py
  - [ ] 实现CalibrationTargetsWidget类，可视化目标
  - [ ] 添加目标值vs模型输出（模拟值及其95%CI）对比图表
  - [ ] 创建年龄性别分层的可视化
  - [ ] 实现交互式目标值编辑功能
  - [ ] 添加校准进度和质量可视化

## Dev Notes

### 校准目标数据结构

```python
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
from enum import Enum

class TargetType(Enum):
    ADENOMA_PREVALENCE = "adenoma_prevalence"
    CANCER_INCIDENCE = "cancer_incidence"
    CANCER_MORTALITY = "cancer_mortality"
    SCREENING_DETECTION = "screening_detection"
    SURVIVAL_RATE = "survival_rate"

class DataQuality(Enum):
    HIGH = "high"           # 高质量数据（大样本、多中心）
    MEDIUM = "medium"       # 中等质量数据（中等样本）
    LOW = "low"            # 低质量数据（小样本、单中心）
    UNCERTAIN = "uncertain" # 不确定质量

@dataclass
class CalibrationTarget:
    target_type: TargetType
    age_group: str          # "50-54", "55-59", etc.
    gender: str             # "male", "female", "both"
    value: float
    standard_error: Optional[float] = None
    confidence_interval: Optional[Tuple[float, float]] = None
    sample_size: Optional[int] = None
    data_source: str = ""
    publication_year: int = 2023
    data_quality: DataQuality = DataQuality.MEDIUM
    weight: float = 1.0
    priority: int = 1       # 1=highest, 5=lowest
```

### 校准目标管理系统

```python
class CalibrationTargets:
    def __init__(self):
        self.targets = {}  # {target_type: {age_group: {gender: CalibrationTarget}}}
        self.metadata = {}
        self.data_sources = []
  
    def load_targets_from_file(self, file_path: str, file_format: str = "auto"):
        """从文件加载校准目标"""
  
        if file_format == "auto":
            file_format = self._detect_file_format(file_path)
  
        if file_format == "csv":
            df = pd.read_csv(file_path)
        elif file_format == "excel":
            df = pd.read_excel(file_path)
        elif file_format == "json":
            df = pd.read_json(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_format}")
  
        # 验证数据格式
        self._validate_target_data(df)
  
        # 转换为CalibrationTarget对象
        for _, row in df.iterrows():
            target = CalibrationTarget(
                target_type=TargetType(row['target_type']),
                age_group=row['age_group'],
                gender=row['gender'],
                value=row['value'],
                standard_error=row.get('standard_error'),
                confidence_interval=self._parse_ci(row.get('confidence_interval')),
                sample_size=row.get('sample_size'),
                data_source=row.get('data_source', ''),
                publication_year=row.get('publication_year', 2023),
                data_quality=DataQuality(row.get('data_quality', 'medium')),
                weight=row.get('weight', 1.0),
                priority=row.get('priority', 1)
            )
      
            self.add_target(target)
  
    def add_target(self, target: CalibrationTarget):
        """添加校准目标"""
        target_type = target.target_type
        age_group = target.age_group
        gender = target.gender
  
        if target_type not in self.targets:
            self.targets[target_type] = {}
        if age_group not in self.targets[target_type]:
            self.targets[target_type][age_group] = {}
  
        self.targets[target_type][age_group][gender] = target
  
    def get_target(
        self, 
        target_type: TargetType, 
        age_group: str, 
        gender: str
    ) -> Optional[CalibrationTarget]:
        """获取特定的校准目标"""
        return (self.targets
                .get(target_type, {})
                .get(age_group, {})
                .get(gender))
  
    def get_targets_by_type(self, target_type: TargetType) -> List[CalibrationTarget]:
        """获取特定类型的所有校准目标"""
        targets = []
        if target_type in self.targets:
            for age_group_dict in self.targets[target_type].values():
                for target in age_group_dict.values():
                    targets.append(target)
        return targets
  
    def interpolate_age_specific_targets(
        self, 
        target_type: TargetType, 
        gender: str,
        target_ages: List[int]
    ) -> Dict[int, float]:
        """插值计算特定年龄的目标值"""
  
        # 获取现有的年龄组数据
        existing_targets = []
        for age_group, gender_dict in self.targets.get(target_type, {}).items():
            if gender in gender_dict:
                target = gender_dict[gender]
                age_mid = self._get_age_group_midpoint(age_group)
                existing_targets.append((age_mid, target.value))
  
        if not existing_targets:
            return {}
  
        # 排序
        existing_targets.sort(key=lambda x: x[0])
        ages, values = zip(*existing_targets)
  
        # 线性插值
        interpolated_values = {}
        for target_age in target_ages:
            if target_age <= ages[0]:
                interpolated_values[target_age] = values[0]
            elif target_age >= ages[-1]:
                interpolated_values[target_age] = values[-1]
            else:
                # 线性插值
                for i in range(len(ages) - 1):
                    if ages[i] <= target_age <= ages[i + 1]:
                        # 线性插值公式
                        t = (target_age - ages[i]) / (ages[i + 1] - ages[i])
                        interpolated_value = values[i] + t * (values[i + 1] - values[i])
                        interpolated_values[target_age] = interpolated_value
                        break
  
        return interpolated_values
  
    def _get_age_group_midpoint(self, age_group: str) -> float:
        """获取年龄组中点"""
        if '-' in age_group:
            start, end = age_group.split('-')
            return (int(start) + int(end)) / 2
        elif '+' in age_group:
            start = int(age_group.replace('+', ''))
            return start + 5  # 假设开放区间的中点
        else:
            return float(age_group)
```

### 目标权重管理

```python
class TargetWeightManager:
    def __init__(self):
        self.weight_schemes = {}
        self.current_scheme = "default"
  
    def create_weight_scheme(self, scheme_name: str, weights: Dict[str, float]):
        """创建权重方案"""
        self.weight_schemes[scheme_name] = weights
  
    def calculate_automatic_weights(
        self, 
        targets: List[CalibrationTarget]
    ) -> Dict[str, float]:
        """基于数据质量自动计算权重"""
  
        weights = {}
  
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
      
            # 基础权重
            base_weight = 1.0
      
            # 数据质量调整
            quality_multipliers = {
                DataQuality.HIGH: 2.0,
                DataQuality.MEDIUM: 1.0,
                DataQuality.LOW: 0.5,
                DataQuality.UNCERTAIN: 0.2
            }
            quality_weight = quality_multipliers.get(target.data_quality, 1.0)
      
            # 样本量调整
            if target.sample_size:
                # 对数缩放样本量权重
                sample_weight = np.log10(max(target.sample_size, 10)) / 4  # 标准化到合理范围
            else:
                sample_weight = 0.5  # 未知样本量的默认权重
      
            # 标准误差调整（标准误差越小，权重越高）
            if target.standard_error and target.standard_error > 0:
                se_weight = 1 / (1 + target.standard_error)
            else:
                se_weight = 1.0
      
            # 优先级调整
            priority_weight = 6 - target.priority  # 优先级1=权重5，优先级5=权重1
      
            # 综合权重
            final_weight = (base_weight * quality_weight * 
                          sample_weight * se_weight * priority_weight)
      
            weights[target_key] = final_weight
  
        # 标准化权重（使总和为目标数量）
        total_weight = sum(weights.values())
        if total_weight > 0:
            normalization_factor = len(weights) / total_weight
            weights = {k: v * normalization_factor for k, v in weights.items()}
  
        return weights
  
    def optimize_weights(
        self, 
        targets: List[CalibrationTarget],
        model_outputs: Dict[str, float],
        optimization_method: str = "inverse_variance"
    ) -> Dict[str, float]:
        """优化权重以最小化校准误差"""
  
        if optimization_method == "inverse_variance":
            return self._inverse_variance_weights(targets)
        elif optimization_method == "equal":
            return self._equal_weights(targets)
        elif optimization_method == "adaptive":
            return self._adaptive_weights(targets, model_outputs)
        else:
            raise ValueError(f"Unknown optimization method: {optimization_method}")
  
    def _inverse_variance_weights(self, targets: List[CalibrationTarget]) -> Dict[str, float]:
        """逆方差权重"""
        weights = {}
  
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
      
            if target.standard_error and target.standard_error > 0:
                # 逆方差权重
                variance = target.standard_error ** 2
                weight = 1 / variance
            else:
                # 默认权重
                weight = 1.0
      
            weights[target_key] = weight
  
        return weights
```

### 目标比较器

```python
class TargetComparator:
    def __init__(self, targets: CalibrationTargets):
        self.targets = targets
  
    def compare_outputs(
        self, 
        model_outputs: Dict[str, float]
    ) -> Dict[str, Any]:
        """比较模型输出与校准目标"""
  
        comparison_results = {
            'overall_fit': {},
            'target_specific_fit': {},
            'statistical_tests': {},
            'recommendations': []
        }
  
        # 按目标类型分组比较
        for target_type in TargetType:
            targets_of_type = self.targets.get_targets_by_type(target_type)
            if not targets_of_type:
                continue
      
            type_results = self._compare_target_type(target_type, targets_of_type, model_outputs)
            comparison_results['target_specific_fit'][target_type.value] = type_results
  
        # 计算总体拟合度
        comparison_results['overall_fit'] = self._calculate_overall_fit(comparison_results)
  
        # 生成改进建议
        comparison_results['recommendations'] = self._generate_recommendations(comparison_results)
  
        return comparison_results
  
    def _compare_target_type(
        self, 
        target_type: TargetType, 
        targets: List[CalibrationTarget],
        model_outputs: Dict[str, float]
    ) -> Dict[str, Any]:
        """比较特定类型的目标"""
  
        observed_values = []
        expected_values = []
        weights = []
  
        for target in targets:
            target_key = f"{target_type.value}_{target.age_group}_{target.gender}"
      
            if target_key in model_outputs:
                observed_values.append(model_outputs[target_key])
                expected_values.append(target.value)
                weights.append(target.weight)
  
        if not observed_values:
            return {'error': 'No matching model outputs found'}
  
        observed = np.array(observed_values)
        expected = np.array(expected_values)
        weights = np.array(weights)
  
        # 计算各种拟合指标
        results = {}
  
        # 加权均方误差
        wmse = np.average((observed - expected) ** 2, weights=weights)
        results['weighted_mse'] = wmse
  
        # 加权平均绝对误差
        wmae = np.average(np.abs(observed - expected), weights=weights)
        results['weighted_mae'] = wmae
  
        # 加权平均绝对百分比误差
        wmape = np.average(np.abs((observed - expected) / (expected + 1e-8)), weights=weights) * 100
        results['weighted_mape'] = wmape
  
        # 相关系数
        correlation = np.corrcoef(observed, expected)[0, 1]
        results['correlation'] = correlation
  
        # 卡方拟合优度检验
        chi2_stat, chi2_p = self._chi_square_goodness_of_fit(observed, expected, weights)
        results['chi2_statistic'] = chi2_stat
        results['chi2_p_value'] = chi2_p
  
        # 拟合质量评级
        results['fit_quality'] = self._assess_fit_quality(wmape, correlation, chi2_p)
  
        return results
  
    def _chi_square_goodness_of_fit(
        self, 
        observed: np.ndarray, 
        expected: np.ndarray, 
        weights: np.ndarray
    ) -> Tuple[float, float]:
        """卡方拟合优度检验"""
        from scipy.stats import chisquare
  
        # 避免除零
        expected_safe = np.where(expected == 0, 1e-8, expected)
  
        # 加权卡方统计量
        chi2_contributions = weights * ((observed - expected_safe) ** 2) / expected_safe
        chi2_stat = np.sum(chi2_contributions)
  
        # 自由度
        df = len(observed) - 1
  
        # p值计算
        from scipy.stats import chi2
        p_value = 1 - chi2.cdf(chi2_stat, df)
  
        return chi2_stat, p_value
  
    def _assess_fit_quality(
        self, 
        wmape: float, 
        correlation: float, 
        chi2_p: float
    ) -> str:
        """评估拟合质量"""
  
        # 综合评分
        score = 0
  
        # MAPE评分
        if wmape < 5:
            score += 3
        elif wmape < 10:
            score += 2
        elif wmape < 20:
            score += 1
  
        # 相关性评分
        if correlation > 0.9:
            score += 3
        elif correlation > 0.8:
            score += 2
        elif correlation > 0.6:
            score += 1
  
        # 卡方检验评分
        if chi2_p > 0.05:
            score += 2
        elif chi2_p > 0.01:
            score += 1
  
        # 质量等级
        if score >= 7:
            return "优秀"
        elif score >= 5:
            return "良好"
        elif score >= 3:
            return "中等"
        else:
            return "较差"
```

### Testing

#### 测试文件位置

- `tests/unit/test_calibration_targets.py`
- `tests/unit/test_target_weights.py`
- `tests/unit/test_target_comparison.py`
- `tests/integration/test_targets_management.py`

#### 测试标准

- 目标数据导入和管理测试
- 年龄性别插值准确性测试
- 权重计算逻辑测试
- 数据质量验证测试
- 目标比较功能测试

#### 测试框架和模式

- 使用合成目标数据测试功能
- 参数化测试验证不同数据格式
- 统计检验验证比较算法
- Mock数据测试边界条件

#### 特定测试要求

- 数据导入准确性: 100%数据完整性
- 插值计算精度: 误差 < 1%
- 权重计算一致性: 重复计算结果相同
- 比较算法准确性: 与标准统计软件结果一致

## Change Log

| Date       | Version | Description  | Author       |
| ---------- | ------- | ------------ | ------------ |
| 2025-07-31 | 1.0     | 初始故事创建 | Scrum Master |

## Dev Agent Record

*此部分将由开发代理在实施过程中填写*

### Agent Model Used

*待填写*

### Debug Log References

*待填写*

### Completion Notes List

*待填写*

### File List

*待填写*

## QA Results

*此部分将由QA代理在完成故事实施的QA审查后填写*
